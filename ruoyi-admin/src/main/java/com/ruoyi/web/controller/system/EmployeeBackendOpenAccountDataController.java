package com.ruoyi.web.controller.system;

import com.alibaba.druid.util.StringUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/employee_backend_data")
public class EmployeeBackendOpenAccountDataController {
    @Autowired
    private IEnterInformationService enterInformationService;
    @Autowired
    private ISysUserService userService;
    @PostMapping(value = "/queryOpenAccountAmount")
    public AjaxResult queryOpenAccountAmount() {
        Long deptId = 104L;
        List<SysUser> sysUsers = userService.selectUserListByDeptId(deptId);
        //去除dept对象中deptName为商务部的数据
        sysUsers.removeIf(sysUser -> sysUser.getDept()!=null && StringUtils.equals("商务部", sysUser.getDept().getDeptName()));
        //然后根据SysUser中的dept对象中deptName将用户分组,分组list中只需保存nickName就可以. 用户将会被分为商务一部,商务二部,商务三部和商务四部
        Map<String, List<String>> deptUserNickNamesMap = sysUsers.stream().collect(Collectors.groupingBy(sysUser -> sysUser.getDept().getDeptName(), Collectors.mapping(SysUser::getNickName, Collectors.toList())));
        //创建统计时间范围,昨天,7天,本月,本年
        Calendar calendar = getZeroTime();
        Date todayZero = calendar.getTime();
        //创建昨天0点0分0秒
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterdayZero = calendar.getTime();
        calendar = getZeroTime();
        //创建距离今日0点7天的时间
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date sevenDaysAgo = calendar.getTime();
        //创建这个月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisMonthBeginTime = calendar.getTime();
        //创建本年1月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisYearBeginTime = calendar.getTime();
        //创建统计时间范围,昨天,7天,本月,本年的开始结束时间对;statType为'daily','weekly','monthly','yearly'
        Map<String, Date[]> timeRangeMap = new HashMap<>();
        timeRangeMap.put("daily", new Date[]{yesterdayZero, todayZero});
        timeRangeMap.put("weekly", new Date[]{sevenDaysAgo, todayZero});
        timeRangeMap.put("monthly", new Date[]{thisMonthBeginTime, todayZero});
        timeRangeMap.put("yearly", new Date[]{thisYearBeginTime, todayZero});
        // 存储最终结果
        Map<String, Map<String, Object>> resultMap = new HashMap<>();

        // 先按timeRangeMap循环，计算每个时间范围的总交易额
        Map<String, Integer> totalOpenAccountMap = new HashMap<>();
        for (Map.Entry<String, Date[]> entry1 : timeRangeMap.entrySet()) {
            String statType = entry1.getKey();
            Date[] timeRange = entry1.getValue();
            Date startTime = timeRange[0];
            Date endTime = timeRange[1];

            // 查询该时间范围的总开户数并按statType放入totalOpenAccountMap
        }

        // 再按部门deptUserNickNamesMap循环,再按timeRangeMap循环
        for (Map.Entry<String, List<String>> entry : deptUserNickNamesMap.entrySet()) {
            String deptName = entry.getKey();
            List<String> nickNameList = entry.getValue();
            //timeRangeMap
            for (Map.Entry<String, Date[]> entry1 : timeRangeMap.entrySet()) {
                String statType = entry1.getKey();
                Date[] timeRange = entry1.getValue();
                Date startTime = timeRange[0];
                Date endTime = timeRange[1];
            }
        }



        return AjaxResult.success();
    }


    private static Calendar getZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }
}
